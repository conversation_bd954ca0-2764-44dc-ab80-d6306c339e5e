"""
LLM模型配置文件

用户可以直接修改这个文件中的配置来调整模型选择策略。
配置格式：
- name: 模型名称（字符串，对应 src/common/llms.py 中的常量值）
- weight: 权重，用于同级模型的随机选择（数值越大被选中概率越高）

模型名称参考（llms.py中的常量值）：
- deepseek-reasoner (deepseek_r1)
- deepseek-chat (deepseek_chat)
- kimi-k2-turbo-preview (kimi_k2_turbo)
- glm-4.5 (glm_4p5)
- qwen-turbo-2025-04-28 (turbo_llm)
- qwen-plus-2025-04-28 (plus_llm)
- qwen3-235b-a22b-instruct-2507 (qwen3_instruct)
- qwen3-235b-a22b-thinking-2507 (qwen3_thinking)
- qwen3-30b-a3b-instruct-2507 (qwen3_instruct_30b)
- qwen3-30b-a3b-thinking-2507 (qwen3_thinking_30b)
"""

# deepseek
deepseek_r1 = "deepseek-reasoner"
deepseek_r1_ppio = "deepseek-reasoner-ppio"  # 类型解析有点问题
deepseek_r1_ali = "deepseek-reasoner-ali"
deepseek_chat = "deepseek-chat"

# kimi
kimi_k2_ppio = "kimi-k2-ppio"
kimi_k2_turbo = "kimi-k2-turbo-preview"

# glm
glm_4p5_air = "glm-4.5-air"  # glm的官方api jsonmode有问题
glm_4p5 = "glm-4.5"  # glm的官方api jsonmode有问题
glm_4p5_ppio = "glm-4.5-ppio"  # ppio的封装 jsonmode没问题 (会不会是think开关的问题)

# qwen-ali
old_llm = "qwen-turbo-0919"
turbo_llm = "qwen-turbo-2025-04-28"
plus_llm = "qwen-plus-2025-04-28"

# qwen-open
qwen3_instruct_ppio = "qwen3-235b-a22b-instruct-2507-ppio"  # 有时候乱码
qwen3_thinking_ppio = "qwen3-235b-a22b-thinking-2507-ppio"  # 这个不支持json mode 有时候乱码？

qwen3_instruct = "qwen3-235b-a22b-instruct-2507"
qwen3_thinking = "qwen3-235b-a22b-thinking-2507"  # 这个不支持json mode

qwen3_instruct_30b = "qwen3-30b-a3b-instruct-2507"
qwen3_thinking_30b = "qwen3-30b-a3b-thinking-2507"  # 这个不支持json mode


# turbo升级版flash
qwen_flash = "qwen-flash"
qwen_flash_thinking = "qwen-flash:thinking"

# select
working_chat = qwen3_instruct_30b
working_thinking = qwen3_thinking_30b

working_chat_plus = qwen_flash
working_thinking_plus = qwen_flash_thinking

working_chat_pro = qwen3_instruct
working_thinking_pro = qwen3_thinking


# LLM配置 - 用户可以直接修改这里的配置
# 新的数字化tier配置格式
LLM_CONFIG = {
    "json_mode": [
        {"name": working_chat, "weight": 1.0, "tier": 1},
        {"name": working_chat_plus, "weight": 1.0, "tier": 2},
        {"name": working_chat_pro, "weight": 1.0, "tier": 3},
    ],
    "text_mode": [
        {"name": working_thinking, "weight": 1.0, "tier": 1},
        {"name": working_thinking_plus, "weight": 1.0, "tier": 2},
        {"name": working_thinking_pro, "weight": 1.0, "tier": 3},
    ],
}


def validate_config():
    """验证配置的有效性"""
    errors = []

    for mode_name, models in LLM_CONFIG.items():
        if mode_name not in ["json_mode", "text_mode"]:
            errors.append(f"无效的模式名称: {mode_name}")
            continue

        if not isinstance(models, list):
            errors.append(f"{mode_name} 必须是列表")
            continue

        for i, model_info in enumerate(models):
            if not isinstance(model_info, dict):
                errors.append(f"{mode_name}[{i}] 必须是字典")
                continue

            # 检查必需字段
            if "name" not in model_info:
                errors.append(f"{mode_name}[{i}] 缺少 'name' 字段")

            if "weight" not in model_info:
                errors.append(f"{mode_name}[{i}] 缺少 'weight' 字段")
            elif not isinstance(model_info["weight"], (int, float)) or model_info["weight"] <= 0:
                errors.append(f"{mode_name}[{i}] 'weight' 必须是正数")

            if "tier" not in model_info:
                errors.append(f"{mode_name}[{i}] 缺少 'tier' 字段")
            elif not isinstance(model_info["tier"], int) or model_info["tier"] < 1:
                errors.append(f"{mode_name}[{i}] 'tier' 必须是大于等于1的整数")

    return errors
