"""任务相关的API路由。"""

import asyncio
import json
import logging
import traceback
import uuid
from datetime import datetime
from typing import Any, Dict

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from langchain_core.messages import HumanMessage

from src.api.checkpoint_manager import checkpoint_manager
from src.api.models.task_models import (
    ErrorResponse,
    TaskCreateRequest,
    TaskDetailResponse,
    TaskListResponse,
    TaskResponse,
)
from src.langgraph.common.utils.socket_router import ensure_socket_patched
from src.langgraph.common.utils.time_utils import get_api_timestamp
from src.langgraph.graph import zego_graph
from src.langgraph.nodes.common.types import z_to_json_str
from src.langgraph.zego_tools.ocean_executor import ocean_connection, themis_connection

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/tasks", tags=["tasks"])

ensure_socket_patched()


@router.get("/health/db")
async def test_database_connection():
    """测试数据库连接是否正常"""
    try:
        # 使用全局ocean_connection实例进行测试
        is_connected = await ocean_connection.test_connection()

        if is_connected:
            return {"status": "ok", "message": "数据库连接正常"}
        else:
            return {"status": "error", "message": "数据库连接失败"}

    except Exception as e:
        logger.error(f"数据库连接测试异常: {e}")
        return {"status": "error", "message": f"数据库连接测试异常: {str(e)}"}


@router.get("/debug/db")
async def debug_database_connection():
    """详细调试数据库连接问题"""
    import os
    import traceback

    debug_info = {
        "env_vars": {
            "ZEGO_MYSQL_URL": os.getenv("ZEGO_MYSQL_URL"),
            "ZEGO_MYSQL_PORT": os.getenv("ZEGO_MYSQL_PORT"),
            "ZEGO_MYSQL_USER": os.getenv("ZEGO_MYSQL_USER"),
            "ZEGO_MYSQL_PASSWORD": "***" if os.getenv("ZEGO_MYSQL_PASSWORD") else None,
            "ZEGO_SOCKS_PROXY": os.getenv("ZEGO_SOCKS_PROXY"),
            "ZEGO_SOCKS_PORT": os.getenv("ZEGO_SOCKS_PORT"),
        },
        "connection_info": {
            "connection_type": ocean_connection.params.name,
            "host_key": ocean_connection.params.host_key,
            "port_key": ocean_connection.params.port_key,
        },
        "test_results": [],
    }

    # 测试1: 直接执行查询
    try:
        logger.info("开始调试测试1: 直接执行查询")
        result = await ocean_connection.execute_query("SELECT 1 as test")
        debug_info["test_results"].append(
            {
                "test": "direct_query",
                "success": result.is_successful,
                "error": result.error_message if not result.is_successful else None,
                "error_code": result.error_code if not result.is_successful else None,
            }
        )
    except Exception as e:
        debug_info["test_results"].append(
            {
                "test": "direct_query",
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc(),
            }
        )

    # 测试2: test_connection方法
    try:
        logger.info("开始调试测试2: test_connection方法")
        is_connected = await ocean_connection.test_connection(timeout_seconds=10)
        debug_info["test_results"].append(
            {
                "test": "test_connection",
                "success": is_connected,
            }
        )
    except Exception as e:
        debug_info["test_results"].append(
            {
                "test": "test_connection",
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc(),
            }
        )

    # 测试3: 尝试不使用SOCKS的连接
    try:
        logger.info("开始调试测试3: 不使用SOCKS的直接连接")
        import aiomysql

        host = os.getenv("ZEGO_MYSQL_URL")
        port = int(os.getenv("ZEGO_MYSQL_PORT", "3306"))
        user = os.getenv("ZEGO_MYSQL_USER")
        password = os.getenv("ZEGO_MYSQL_PASSWORD") or ""

        # 直接连接，不使用SOCKS
        conn = await aiomysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset="utf8mb4",
            autocommit=True,
            connect_timeout=10,
        )

        async with conn.cursor() as cursor:
            await cursor.execute("SELECT 1 as test")
            result = await cursor.fetchall()

        conn.close()

        debug_info["test_results"].append(
            {
                "test": "direct_no_socks",
                "success": True,
                "result": str(result),
            }
        )

    except Exception as e:
        debug_info["test_results"].append(
            {
                "test": "direct_no_socks",
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc(),
            }
        )

    # 测试4: 使用SOCKS代理的连接
    try:
        logger.info("开始调试测试4: 使用SOCKS代理的连接")
        from src.langgraph.common.utils.socket_router import db_socks_context

        host = os.getenv("ZEGO_MYSQL_URL")
        port = int(os.getenv("ZEGO_MYSQL_PORT", "3306"))
        user = os.getenv("ZEGO_MYSQL_USER")
        password = os.getenv("ZEGO_MYSQL_PASSWORD") or ""

        # 使用SOCKS代理连接
        async with db_socks_context():
            conn = await aiomysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                charset="utf8mb4",
                autocommit=True,
                connect_timeout=10,
            )

            async with conn.cursor() as cursor:
                await cursor.execute("SELECT 1 as test")
                result = await cursor.fetchall()

            conn.close()

        debug_info["test_results"].append(
            {
                "test": "socks_proxy",
                "success": True,
                "result": str(result),
            }
        )

    except Exception as e:
        debug_info["test_results"].append(
            {
                "test": "socks_proxy",
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc(),
            }
        )

    return debug_info


@router.get("/health/themis")
async def test_themis_connection():
    """测试Themis数据库连接是否正常"""
    try:
        # 测试Themis连接（不使用SOCKS）
        result = await themis_connection.execute_query("SELECT 1 as test", themis_db="sdk")

        if result.is_successful:
            return {"status": "ok", "message": "Themis数据库连接正常", "data": result.data.to_dict()}
        else:
            return {"status": "error", "message": f"Themis数据库连接失败: {result.error_message}"}

    except Exception as e:
        logger.error(f"Themis数据库连接测试异常: {e}")
        return {"status": "error", "message": f"Themis数据库连接测试异常: {str(e)}"}


@router.get("/debug/socks")
async def debug_socks_proxy():
    """调试SOCKS代理配置"""
    import os

    from src.langgraph.common.utils.socket_router import _get_proxy_config

    # 获取SOCKS配置
    host, port, username, password = _get_proxy_config()

    debug_info = {
        "socks_config": {
            "host": host,
            "port": port,
            "username": username,
            "password": "***" if password else None,
        },
        "env_vars": {
            "ZEGO_SOCKS_PROXY": os.getenv("ZEGO_SOCKS_PROXY"),
            "ZEGO_SOCKS_PORT": os.getenv("ZEGO_SOCKS_PORT"),
            "ZEGO_SOCKS_PROXY_USER": os.getenv("ZEGO_SOCKS_PROXY_USER"),
            "ZEGO_SOCKS_PROXY_PASSWORD": "***" if os.getenv("ZEGO_SOCKS_PROXY_PASSWORD") else None,
        },
        "socket_patched": True,  # 我们已经调用了ensure_socket_patched()
    }

    # 测试SOCKS代理是否可用
    try:
        import socket

        import socks

        # 创建SOCKS代理socket测试
        sock = socks.socksocket()
        sock.set_proxy(socks.SOCKS5, host, port, username=username, password=password)
        sock.settimeout(5)

        # 尝试连接到一个公网地址测试SOCKS
        sock.connect(("*******", 53))  # Google DNS
        sock.close()

        debug_info["socks_test"] = {"success": True, "message": "SOCKS代理连接测试成功"}

    except Exception as e:
        debug_info["socks_test"] = {"success": False, "error": str(e)}

    # 测试4: 检查socket patching状态
    try:
        logger.info("开始调试测试4: 检查socket patching状态")
        import socket

        # 检查socket.socket是否被patched
        original_socket = socket.socket
        debug_info["socket_patching"] = {
            "socket_class": str(type(original_socket)),
            "socket_module": str(original_socket.__module__),
            "is_patched": hasattr(original_socket, "_original_socket"),
        }

        import src.langgraph.common.utils.socket_router as socket_router_module

        # 检查socket_router的状态
        from src.langgraph.common.utils.socket_router import _get_proxy_config

        debug_info["socket_router_status"] = {
            "is_patched": getattr(socket_router_module, "_patched", False),
            "proxy_config": _get_proxy_config(),
        }

    except Exception as e:
        debug_info["socket_patching"] = {
            "error": str(e),
            "traceback": traceback.format_exc(),
        }

    # 测试5: 直接测试SOCKS连接
    try:
        logger.info("开始调试测试5: 直接测试SOCKS连接")
        import socks

        # 创建SOCKS socket
        sock = socks.socksocket()
        sock.set_proxy(
            socks.SOCKS5,
            os.getenv("ZEGO_SOCKS_PROXY"),
            int(os.getenv("ZEGO_SOCKS_PORT", "1080")),
            username=os.getenv("ZEGO_SOCKS_PROXY_USER"),
            password=os.getenv("ZEGO_SOCKS_PROXY_PASSWORD"),
        )
        sock.settimeout(10)

        # 尝试连接到数据库服务器
        db_host = os.getenv("ZEGO_MYSQL_URL")
        db_port = int(os.getenv("ZEGO_MYSQL_PORT", "3306"))

        sock.connect((db_host, db_port))
        sock.close()

        debug_info["direct_socks_test"] = {"success": True, "message": "直接SOCKS连接到数据库成功"}

    except Exception as e:
        debug_info["direct_socks_test"] = {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
        }

    return debug_info


@router.post("/", response_model=TaskResponse)
async def create_task(request: TaskCreateRequest):
    """创建新任务。"""
    try:
        # 生成唯一的thread_id
        thread_id = f"web_{uuid.uuid4()}"

        # 构建配置
        config = {
            "configurable": {
                "thread_id": thread_id,
                "max_parallel_workers": request.max_parallel_workers,
            },
            "recursion_limit": request.recursion_limit,
        }

        # 启动任务（异步执行，不等待完成）
        asyncio.create_task(_execute_task_async(request.user_input, config))

        # 立即返回任务信息
        response = TaskResponse(
            thread_id=thread_id,
            status="pending",
            created_at=datetime.now(),
            user_input=request.user_input,
            config=config,
        )

        logger.info(f"Created task {thread_id} with input: {request.user_input[:100]}...")
        return response

    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=TaskListResponse)
async def get_tasks(
    limit: int = Query(default=20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(default=0, ge=0, description="偏移量"),
):
    """获取任务列表。"""
    try:
        tasks = await checkpoint_manager.get_task_list(limit=limit, offset=offset)

        # 转换为响应模型
        task_responses = []
        for task in tasks:
            task_response = TaskResponse(
                thread_id=task["thread_id"],
                status=task["status"],
                created_at=task["created_at"],
                user_input=task["user_input"],
                config=task["config"],
            )
            task_responses.append(task_response)

        response = TaskListResponse(tasks=task_responses, total=len(task_responses))  # 简化实现，实际应该查询总数

        logger.info(f"Retrieved {len(task_responses)} tasks")
        return response

    except Exception as e:
        logger.error(f"Failed to get tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{thread_id}", response_model=TaskDetailResponse)
async def get_task_detail(thread_id: str):
    """获取任务详情。"""
    try:
        task_detail = await checkpoint_manager.get_task_detail(thread_id)

        if not task_detail:
            raise HTTPException(status_code=404, detail=f"Task {thread_id} not found")

        response = TaskDetailResponse(
            thread_id=task_detail["thread_id"],
            status=task_detail["status"],
            created_at=task_detail["created_at"],
            updated_at=task_detail.get("updated_at"),
            user_input=task_detail["user_input"],
            config=task_detail["config"],
            checkpoints=task_detail.get("checkpoints", []),
            messages=task_detail.get("messages", []),
        )

        logger.info(f"Retrieved task detail for {thread_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task detail for {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{thread_id}/stream")
async def stream_task_execution(thread_id: str, request: TaskCreateRequest):
    """流式执行任务，返回Server-Sent Events。"""
    try:
        # 构建配置
        config = {
            "configurable": {
                "thread_id": thread_id,
                "max_parallel_workers": request.max_parallel_workers,
            },
            "recursion_limit": request.recursion_limit,
        }

        # 生成流式响应
        async def event_generator():
            try:
                logger.info(f"Starting streaming task execution for thread_id: {thread_id}")

                input_state = {"messages": [HumanMessage(content=request.user_input)]}

                async with zego_graph() as graph:
                    # 使用多种stream_mode获取完整信息
                    stream_mode = ["values", "messages", "updates", "custom"]

                    async for ev in graph.astream(input_state, config, stream_mode=stream_mode, debug=False):
                        # 解析事件类型和值
                        if isinstance(ev, tuple) and len(ev) == 2:
                            etype, evalue = ev
                        else:
                            etype, evalue = "updates", ev

                        # 构建事件对象
                        event_obj = {
                            "type": etype,
                            "value": evalue,
                            "thread_id": thread_id,
                            "timestamp": get_api_timestamp(),
                        }

                        # 发送SSE数据，使用安全序列化（严格输出 JSON 字符串）
                        try:
                            yield z_to_json_str(event_obj)
                        except Exception as e:
                            logger.error(f"Failed to serialize event object: {e}")
                            # 发送简化的错误事件（同样输出为 JSON 字符串）
                            error_event = {
                                "type": "serialization_error",
                                "value": f"Failed to serialize event: {str(e)}",
                                "thread_id": thread_id,
                                "timestamp": get_api_timestamp(),
                            }
                            yield z_to_json_str(error_event)

                # 发送成功完成事件
                success_obj = {
                    "type": "success",
                    "value": None,
                    "thread_id": thread_id,
                    "timestamp": get_api_timestamp(),
                }
                yield z_to_json_str(success_obj)

                logger.info(f"Task execution completed for thread_id: {thread_id}")

            except Exception as e:
                logger.error(f"Task execution failed for thread_id {thread_id}: {e}")
                # 发送错误事件
                error_obj = {
                    "type": "error",
                    "value": str(e),
                    "thread_id": thread_id,
                    "timestamp": get_api_timestamp(),
                }
                yield z_to_json_str(error_obj)
            finally:
                # 发送关闭事件
                close_obj = {
                    "type": "close",
                    "value": None,
                    "thread_id": thread_id,
                    "timestamp": get_api_timestamp(),
                }
                yield z_to_json_str(close_obj)

        # 返回流式响应
        headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
        return StreamingResponse(event_generator(), media_type="text/event-stream", headers=headers)

    except Exception as e:
        logger.error(f"Failed to start streaming for {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _execute_task_async(user_input: str, config: Dict[str, Any]):
    """异步执行任务（后台执行，不流式输出）。"""
    try:
        logger.info(f"Starting task execution for thread_id: {config['configurable']['thread_id']}")

        input_state = {"messages": [HumanMessage(content=user_input)]}

        async with zego_graph() as graph:
            # 执行图，但不流式输出（后台执行）
            async for event in graph.astream(input_state, config, stream_mode="updates", debug=False):
                # 这里可以添加进度更新逻辑，比如通过WebSocket推送
                pass

        logger.info(f"Task execution completed for thread_id: {config['configurable']['thread_id']}")

    except Exception as e:
        logger.error(f"Task execution failed for thread_id {config['configurable']['thread_id']}: {e}")
        # 这里可以更新任务状态为失败
